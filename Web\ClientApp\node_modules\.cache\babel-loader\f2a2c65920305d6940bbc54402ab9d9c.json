{"ast": null, "code": "import PouchDB from 'pouchdb';\nimport { EventTypes, events } from 'app/events';\nimport { configuration } from 'features/auth/configuration';\nimport { getUserInfo } from 'features/auth/auth-provider';\nimport { createProblemDetails } from 'utils/problem-details';\nimport { PlantType } from 'api/models/plants';\nimport { CustomerType } from 'api/models/customers';\nimport { DriverTaskType } from 'api/models/driver-tasks';\nimport { ZoneType } from 'api/models/zones';\nimport { OrderType } from 'api/models/orders';\nimport { ColourType } from 'api/models/colour';\nconst obsolete = new PouchDB(configuration.obsolete_database_name);\nobsolete.info().then(info => {\n  console.log('Obsolete db info', info);\n});\nobsolete.destroy().then(response => {\n  console.log('Obsolete db destroyed', response);\n});\nlet localDb = null;\nlet remoteDb = null;\nclass Database {\n  constructor() {\n    this.init().then(() => {\n      events.on(EventTypes.authenticated, this.init.bind(this));\n    });\n  }\n  async init() {\n    if (localDb == null) {\n      localDb = new PouchDB(configuration.app_database_name);\n    }\n    const userInfo = getUserInfo();\n    if (remoteDb == null && userInfo) {\n      const opts = {\n        skip_setup: true,\n        auth: {\n          username: userInfo.name,\n          password: userInfo.password\n        }\n      };\n      remoteDb = new PouchDB(configuration.remote_database_name, opts);\n    }\n    if (remoteDb) {\n      localDb.replicate.from(remoteDb, {\n        retry: true\n      }).on('complete', info => {\n        console.log('Initial replication completed. Commencing sync.', info);\n        if (localDb && remoteDb) {\n          localDb.sync(remoteDb, {\n            live: true,\n            retry: true,\n            batch_size: 1000\n          }).on('complete', () => {\n            console.log('Sync complete');\n          }).on('error', err => {\n            console.error('Sync error');\n            console.error(err);\n            const values = Object.values(err);\n            // this happens on iOS 10/Safari. Use the API keys...\n            if (values.indexOf('_reader access is required for this request') !== -1) {\n              try {\n                //sync;\n              } catch (e) {}\n              localDb = null;\n              events.emit(EventTypes.error, createProblemDetails('Invalid Permissions'));\n            }\n          }).on('change', _ref => {\n            let {\n              change\n            } = _ref;\n            console.log('Sync change');\n            console.log(change);\n            if (Array.isArray(change.docs)) {\n              let deleted = change.docs.some(doc => doc._deleted),\n                ordersSynced = deleted || change.docs.some(doc => doc.type === OrderType),\n                zonesSynced = deleted || change.docs.some(doc => doc.type === ZoneType),\n                customersSynced = deleted || change.docs.some(doc => doc.type === CustomerType),\n                plantsSynced = deleted || change.docs.some(doc => doc.type === PlantType),\n                driverTasksSynced = deleted || change.docs.some(doc => doc.type === DriverTaskType),\n                coloursSynced = deleted || change.docs.some(doc => doc.type === ColourType);\n              if (ordersSynced) {\n                events.emit(EventTypes.ordersUpdated);\n              }\n              if (customersSynced) {\n                events.emit(EventTypes.customersUpdated);\n              }\n              if (zonesSynced) {\n                events.emit(EventTypes.zonesUpdated);\n              }\n              if (plantsSynced) {\n                events.emit(EventTypes.plantsUpdated);\n              }\n              if (driverTasksSynced) {\n                events.emit(EventTypes.driverTasksUpdated);\n              }\n            }\n          }).on('paused', info => {\n            console.log('Sync paused');\n            console.log(info);\n          }).catch(err => {\n            console.error('sync error', err);\n          });\n        }\n      });\n    }\n  }\n  get db() {\n    if (!localDb) {\n      throw createProblemDetails('Database not initialized');\n    }\n    return localDb;\n  }\n}\nexport const database = new Database();", "map": {"version": 3, "names": ["PouchDB", "EventTypes", "events", "configuration", "getUserInfo", "createProblemDetails", "PlantType", "CustomerType", "DriverTaskType", "ZoneType", "OrderType", "ColourType", "obsolete", "obsolete_database_name", "info", "then", "console", "log", "destroy", "response", "localDb", "remoteDb", "Database", "constructor", "init", "on", "authenticated", "bind", "app_database_name", "userInfo", "opts", "skip_setup", "auth", "username", "name", "password", "remote_database_name", "replicate", "from", "retry", "sync", "live", "batch_size", "err", "error", "values", "Object", "indexOf", "e", "emit", "change", "Array", "isArray", "docs", "deleted", "some", "doc", "_deleted", "ordersSynced", "type", "zonesSynced", "customersSynced", "plantsSynced", "driverTasksSynced", "coloursSynced", "ordersUpdated", "customersUpdated", "zonesUpdated", "plantsUpdated", "driverTasksUpdated", "catch", "db", "database"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/app/database.ts"], "sourcesContent": ["import PouchDB from 'pouchdb';\r\nimport { EventTypes, events } from 'app/events';\r\nimport { configuration } from 'features/auth/configuration';\r\nimport { getUserInfo } from 'features/auth/auth-provider';\r\nimport { createProblemDetails } from 'utils/problem-details';\r\nimport { PlantType } from 'api/models/plants';\r\nimport { CustomerType } from 'api/models/customers';\r\nimport { DriverTaskType } from 'api/models/driver-tasks';\r\nimport { ZoneType } from 'api/models/zones';\r\nimport { OrderType } from 'api/models/orders';\r\nimport { ColourType } from 'api/models/colour';\r\n\r\nconst obsolete = new PouchDB(configuration.obsolete_database_name);\r\nobsolete.info().then((info) => {\r\n  console.log('Obsolete db info', info);\r\n});\r\nobsolete.destroy().then((response) => {\r\n  console.log('Obsolete db destroyed', response);\r\n});\r\n\r\nlet localDb: PouchDB.Database | null = null;\r\nlet remoteDb: PouchDB.Database | null = null;\r\n\r\nclass Database {\r\n  constructor() {\r\n    this.init().then(() => {\r\n      events.on(EventTypes.authenticated, this.init.bind(this));\r\n    });\r\n  }\r\n\r\n  async init() {\r\n    if (localDb == null) {\r\n      localDb = new PouchDB(configuration.app_database_name);\r\n    }\r\n\r\n    const userInfo = getUserInfo();\r\n    if (remoteDb == null && userInfo) {\r\n      const opts = {\r\n        skip_setup: true,\r\n        auth: { username: userInfo.name, password: userInfo.password },\r\n      };\r\n\r\n      remoteDb = new PouchDB(configuration.remote_database_name, opts);\r\n    }\r\n\r\n    if (remoteDb) {\r\n      localDb.replicate\r\n        .from(remoteDb, { retry: true })\r\n        .on('complete', (info) => {\r\n          console.log('Initial replication completed. Commencing sync.', info);\r\n          if (localDb && remoteDb) {\r\n            localDb\r\n              .sync(remoteDb, { live: true, retry: true, batch_size: 1000 })\r\n              .on('complete', () => {\r\n                console.log('Sync complete');\r\n              })\r\n              .on('error', (err) => {\r\n                console.error('Sync error');\r\n                console.error(err);\r\n                const values = Object.values(err);\r\n                // this happens on iOS 10/Safari. Use the API keys...\r\n                if (\r\n                  values.indexOf(\r\n                    '_reader access is required for this request'\r\n                  ) !== -1\r\n                ) {\r\n                  try {\r\n                    //sync;\r\n                  } catch (e) {}\r\n\r\n                  localDb = null;\r\n                  events.emit(\r\n                    EventTypes.error,\r\n                    createProblemDetails('Invalid Permissions')\r\n                  );\r\n                }\r\n              })\r\n              .on('change', ({ change }) => {\r\n                console.log('Sync change');\r\n                console.log(change);\r\n                if (Array.isArray(change.docs)) {\r\n                  let deleted = change.docs.some(\r\n                      (doc) => (doc as any)._deleted\r\n                    ),\r\n                    ordersSynced =\r\n                      deleted ||\r\n                      change.docs.some(\r\n                        (doc) => (doc as any).type === OrderType\r\n                      ),\r\n                    zonesSynced =\r\n                      deleted ||\r\n                      change.docs.some((doc) => (doc as any).type === ZoneType),\r\n                    customersSynced =\r\n                      deleted ||\r\n                      change.docs.some(\r\n                        (doc) => (doc as any).type === CustomerType\r\n                      ),\r\n                    plantsSynced =\r\n                      deleted ||\r\n                      change.docs.some(\r\n                        (doc) => (doc as any).type === PlantType\r\n                      ),\r\n                    driverTasksSynced =\r\n                      deleted ||\r\n                      change.docs.some(\r\n                        (doc) => (doc as any).type === DriverTaskType\r\n                      ),\r\n                    coloursSynced =\r\n                      deleted ||\r\n                      change.docs.some(\r\n                        (doc) => (doc as any).type === ColourType\r\n                      );\r\n\r\n                  if (ordersSynced) {\r\n                    events.emit(EventTypes.ordersUpdated);\r\n                  }\r\n\r\n                  if (customersSynced) {\r\n                    events.emit(EventTypes.customersUpdated);\r\n                  }\r\n\r\n                  if (zonesSynced) {\r\n                    events.emit(EventTypes.zonesUpdated);\r\n                  }\r\n\r\n                  if (plantsSynced) {\r\n                    events.emit(EventTypes.plantsUpdated);\r\n                  }\r\n\r\n                  if (driverTasksSynced) {\r\n                    events.emit(EventTypes.driverTasksUpdated);\r\n                  }\r\n                }\r\n              })\r\n              .on('paused', (info) => {\r\n                console.log('Sync paused');\r\n                console.log(info);\r\n              })\r\n              .catch((err) => {\r\n                console.error('sync error', err);\r\n              });\r\n          }\r\n        });\r\n    }\r\n  }\r\n\r\n  get db() {\r\n    if (!localDb) {\r\n      throw createProblemDetails('Database not initialized');\r\n    }\r\n\r\n    return localDb;\r\n  }\r\n}\r\n\r\nexport const database = new Database();\r\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,SAAS;AAC7B,SAASC,UAAU,EAAEC,MAAM,QAAQ,YAAY;AAC/C,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,UAAU,QAAQ,mBAAmB;AAE9C,MAAMC,QAAQ,GAAG,IAAIZ,OAAO,CAACG,aAAa,CAACU,sBAAsB,CAAC;AAClED,QAAQ,CAACE,IAAI,EAAE,CAACC,IAAI,CAAED,IAAI,IAAK;EAC7BE,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEH,IAAI,CAAC;AACvC,CAAC,CAAC;AACFF,QAAQ,CAACM,OAAO,EAAE,CAACH,IAAI,CAAEI,QAAQ,IAAK;EACpCH,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEE,QAAQ,CAAC;AAChD,CAAC,CAAC;AAEF,IAAIC,OAAgC,GAAG,IAAI;AAC3C,IAAIC,QAAiC,GAAG,IAAI;AAE5C,MAAMC,QAAQ,CAAC;EACbC,WAAW,GAAG;IACZ,IAAI,CAACC,IAAI,EAAE,CAACT,IAAI,CAAC,MAAM;MACrBb,MAAM,CAACuB,EAAE,CAACxB,UAAU,CAACyB,aAAa,EAAE,IAAI,CAACF,IAAI,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3D,CAAC,CAAC;EACJ;EAEA,MAAMH,IAAI,GAAG;IACX,IAAIJ,OAAO,IAAI,IAAI,EAAE;MACnBA,OAAO,GAAG,IAAIpB,OAAO,CAACG,aAAa,CAACyB,iBAAiB,CAAC;IACxD;IAEA,MAAMC,QAAQ,GAAGzB,WAAW,EAAE;IAC9B,IAAIiB,QAAQ,IAAI,IAAI,IAAIQ,QAAQ,EAAE;MAChC,MAAMC,IAAI,GAAG;QACXC,UAAU,EAAE,IAAI;QAChBC,IAAI,EAAE;UAAEC,QAAQ,EAAEJ,QAAQ,CAACK,IAAI;UAAEC,QAAQ,EAAEN,QAAQ,CAACM;QAAS;MAC/D,CAAC;MAEDd,QAAQ,GAAG,IAAIrB,OAAO,CAACG,aAAa,CAACiC,oBAAoB,EAAEN,IAAI,CAAC;IAClE;IAEA,IAAIT,QAAQ,EAAE;MACZD,OAAO,CAACiB,SAAS,CACdC,IAAI,CAACjB,QAAQ,EAAE;QAAEkB,KAAK,EAAE;MAAK,CAAC,CAAC,CAC/Bd,EAAE,CAAC,UAAU,EAAGX,IAAI,IAAK;QACxBE,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEH,IAAI,CAAC;QACpE,IAAIM,OAAO,IAAIC,QAAQ,EAAE;UACvBD,OAAO,CACJoB,IAAI,CAACnB,QAAQ,EAAE;YAAEoB,IAAI,EAAE,IAAI;YAAEF,KAAK,EAAE,IAAI;YAAEG,UAAU,EAAE;UAAK,CAAC,CAAC,CAC7DjB,EAAE,CAAC,UAAU,EAAE,MAAM;YACpBT,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;UAC9B,CAAC,CAAC,CACDQ,EAAE,CAAC,OAAO,EAAGkB,GAAG,IAAK;YACpB3B,OAAO,CAAC4B,KAAK,CAAC,YAAY,CAAC;YAC3B5B,OAAO,CAAC4B,KAAK,CAACD,GAAG,CAAC;YAClB,MAAME,MAAM,GAAGC,MAAM,CAACD,MAAM,CAACF,GAAG,CAAC;YACjC;YACA,IACEE,MAAM,CAACE,OAAO,CACZ,6CAA6C,CAC9C,KAAK,CAAC,CAAC,EACR;cACA,IAAI;gBACF;cAAA,CACD,CAAC,OAAOC,CAAC,EAAE,CAAC;cAEb5B,OAAO,GAAG,IAAI;cACdlB,MAAM,CAAC+C,IAAI,CACThD,UAAU,CAAC2C,KAAK,EAChBvC,oBAAoB,CAAC,qBAAqB,CAAC,CAC5C;YACH;UACF,CAAC,CAAC,CACDoB,EAAE,CAAC,QAAQ,EAAE,QAAgB;YAAA,IAAf;cAAEyB;YAAO,CAAC;YACvBlC,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;YAC1BD,OAAO,CAACC,GAAG,CAACiC,MAAM,CAAC;YACnB,IAAIC,KAAK,CAACC,OAAO,CAACF,MAAM,CAACG,IAAI,CAAC,EAAE;cAC9B,IAAIC,OAAO,GAAGJ,MAAM,CAACG,IAAI,CAACE,IAAI,CACzBC,GAAG,IAAMA,GAAG,CAASC,QAAQ,CAC/B;gBACDC,YAAY,GACVJ,OAAO,IACPJ,MAAM,CAACG,IAAI,CAACE,IAAI,CACbC,GAAG,IAAMA,GAAG,CAASG,IAAI,KAAKjD,SAAS,CACzC;gBACHkD,WAAW,GACTN,OAAO,IACPJ,MAAM,CAACG,IAAI,CAACE,IAAI,CAAEC,GAAG,IAAMA,GAAG,CAASG,IAAI,KAAKlD,QAAQ,CAAC;gBAC3DoD,eAAe,GACbP,OAAO,IACPJ,MAAM,CAACG,IAAI,CAACE,IAAI,CACbC,GAAG,IAAMA,GAAG,CAASG,IAAI,KAAKpD,YAAY,CAC5C;gBACHuD,YAAY,GACVR,OAAO,IACPJ,MAAM,CAACG,IAAI,CAACE,IAAI,CACbC,GAAG,IAAMA,GAAG,CAASG,IAAI,KAAKrD,SAAS,CACzC;gBACHyD,iBAAiB,GACfT,OAAO,IACPJ,MAAM,CAACG,IAAI,CAACE,IAAI,CACbC,GAAG,IAAMA,GAAG,CAASG,IAAI,KAAKnD,cAAc,CAC9C;gBACHwD,aAAa,GACXV,OAAO,IACPJ,MAAM,CAACG,IAAI,CAACE,IAAI,CACbC,GAAG,IAAMA,GAAG,CAASG,IAAI,KAAKhD,UAAU,CAC1C;cAEL,IAAI+C,YAAY,EAAE;gBAChBxD,MAAM,CAAC+C,IAAI,CAAChD,UAAU,CAACgE,aAAa,CAAC;cACvC;cAEA,IAAIJ,eAAe,EAAE;gBACnB3D,MAAM,CAAC+C,IAAI,CAAChD,UAAU,CAACiE,gBAAgB,CAAC;cAC1C;cAEA,IAAIN,WAAW,EAAE;gBACf1D,MAAM,CAAC+C,IAAI,CAAChD,UAAU,CAACkE,YAAY,CAAC;cACtC;cAEA,IAAIL,YAAY,EAAE;gBAChB5D,MAAM,CAAC+C,IAAI,CAAChD,UAAU,CAACmE,aAAa,CAAC;cACvC;cAEA,IAAIL,iBAAiB,EAAE;gBACrB7D,MAAM,CAAC+C,IAAI,CAAChD,UAAU,CAACoE,kBAAkB,CAAC;cAC5C;YACF;UACF,CAAC,CAAC,CACD5C,EAAE,CAAC,QAAQ,EAAGX,IAAI,IAAK;YACtBE,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;YAC1BD,OAAO,CAACC,GAAG,CAACH,IAAI,CAAC;UACnB,CAAC,CAAC,CACDwD,KAAK,CAAE3B,GAAG,IAAK;YACd3B,OAAO,CAAC4B,KAAK,CAAC,YAAY,EAAED,GAAG,CAAC;UAClC,CAAC,CAAC;QACN;MACF,CAAC,CAAC;IACN;EACF;EAEA,IAAI4B,EAAE,GAAG;IACP,IAAI,CAACnD,OAAO,EAAE;MACZ,MAAMf,oBAAoB,CAAC,0BAA0B,CAAC;IACxD;IAEA,OAAOe,OAAO;EAChB;AACF;AAEA,OAAO,MAAMoD,QAAQ,GAAG,IAAIlD,QAAQ,EAAE"}, "metadata": {}, "sourceType": "module"}