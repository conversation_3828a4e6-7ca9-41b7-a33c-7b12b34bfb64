{"name": "greenrp", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-pro": "^6.5.2", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/pro-thin-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.2", "@reduxjs/toolkit": "^1.9.2", "@types/node": "^16.18.101", "@types/numeral": "^2.0.2", "@types/pouchdb": "^6.4.2", "@types/react": "^18.0.25", "@types/react-dom": "^18.0.9", "axios": "^1.2.0", "bootstrap": "^5.3.3", "moment": "^2.29.4", "numeral": "^2.0.6", "pouchdb": "^9.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-popper": "^2.3.0", "react-redux": "^7.2.9", "react-router": "^6.4.3", "react-router-dom": "^6.4.3", "react-scripts": "^5.0.1", "reactstrap": "^9.1.5", "sass": "^1.77.6", "sass-loader": "^14.2.1", "serialize-error": "^11.0.0", "typescript": "^4.9.3", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}