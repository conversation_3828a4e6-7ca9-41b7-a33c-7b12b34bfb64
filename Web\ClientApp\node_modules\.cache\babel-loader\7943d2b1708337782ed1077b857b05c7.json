{"ast": null, "code": "export let EventTypes;\n(function (EventTypes) {\n  EventTypes[\"error\"] = \"error\";\n  EventTypes[\"authenticated\"] = \"authenticated\";\n  EventTypes[\"plantsUpdated\"] = \"plants-updated\";\n  EventTypes[\"ordersUpdated\"] = \"orders-updated\";\n  EventTypes[\"customersUpdated\"] = \"customers-updated\";\n  EventTypes[\"zonesUpdated\"] = \"zones-updated\";\n  EventTypes[\"coloursUpdated\"] = \"colours-updated\";\n  EventTypes[\"driverTasksUpdated\"] = \"driver-tasks-updated\";\n})(EventTypes || (EventTypes = {}));\nclass Events {\n  constructor() {\n    this.target = new EventTarget();\n  }\n  on(type, listener) {\n    this.target.addEventListener(type, listener || null);\n  }\n  off(type, listener) {\n    this.target.removeEventListener(type, listener || null);\n  }\n  emit(type, detail) {\n    const event = detail ? new CustomEvent(type, {\n      detail\n    }) : new Event(type);\n    this.target.dispatchEvent(event);\n  }\n}\nexport const events = new Events();", "map": {"version": 3, "names": ["EventTypes", "Events", "target", "EventTarget", "on", "type", "listener", "addEventListener", "off", "removeEventListener", "emit", "detail", "event", "CustomEvent", "Event", "dispatchEvent", "events"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/app/events.ts"], "sourcesContent": ["export enum EventTypes {\r\n  error = 'error',\r\n  authenticated = 'authenticated',\r\n  plantsUpdated = 'plants-updated',\r\n  ordersUpdated = 'orders-updated',\r\n  customersUpdated = 'customers-updated',\r\n  zonesUpdated = 'zones-updated',\r\n  coloursUpdated = 'colours-updated',\r\n  driverTasksUpdated = 'driver-tasks-updated',\r\n}\r\n\r\nclass Events {\r\n  private target = new EventTarget();\r\n\r\n  on(type: EventTypes, listener?: EventListener) {\r\n    this.target.addEventListener(type, listener || null);\r\n  }\r\n\r\n  off(type: EventTypes, listener?: EventListener) {\r\n    this.target.removeEventListener(type, listener || null);\r\n  }\r\n\r\n  emit(type: EventTypes, detail?: any) {\r\n    const event = detail ? new CustomEvent(type, { detail }) : new Event(type);\r\n    this.target.dispatchEvent(event);\r\n  }\r\n}\r\n\r\nexport const events = new Events();\r\n"], "mappings": "AAAA,WAAYA,UAAU;AASrB,WATWA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;EAAVA,UAAU;AAAA,GAAVA,UAAU,KAAVA,UAAU;AAWtB,MAAMC,MAAM,CAAC;EAAA;IAAA,KACHC,MAAM,GAAG,IAAIC,WAAW,EAAE;EAAA;EAElCC,EAAE,CAACC,IAAgB,EAAEC,QAAwB,EAAE;IAC7C,IAAI,CAACJ,MAAM,CAACK,gBAAgB,CAACF,IAAI,EAAEC,QAAQ,IAAI,IAAI,CAAC;EACtD;EAEAE,GAAG,CAACH,IAAgB,EAAEC,QAAwB,EAAE;IAC9C,IAAI,CAACJ,MAAM,CAACO,mBAAmB,CAACJ,IAAI,EAAEC,QAAQ,IAAI,IAAI,CAAC;EACzD;EAEAI,IAAI,CAACL,IAAgB,EAAEM,MAAY,EAAE;IACnC,MAAMC,KAAK,GAAGD,MAAM,GAAG,IAAIE,WAAW,CAACR,IAAI,EAAE;MAAEM;IAAO,CAAC,CAAC,GAAG,IAAIG,KAAK,CAACT,IAAI,CAAC;IAC1E,IAAI,CAACH,MAAM,CAACa,aAAa,CAACH,KAAK,CAAC;EAClC;AACF;AAEA,OAAO,MAAMI,MAAM,GAAG,IAAIf,MAAM,EAAE"}, "metadata": {}, "sourceType": "module"}