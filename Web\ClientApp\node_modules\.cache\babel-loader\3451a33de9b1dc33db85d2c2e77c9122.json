{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\GreenRP\\\\Web\\\\ClientApp\\\\src\\\\features\\\\colours\\\\List.tsx\",\n  _s = $RefreshSig$();\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport function List() {\n  _s();\n  const colours = useSelector(selectColours),\n    {\n      isInRole\n    } = useAuth(),\n    canCreate = isInRole('create:colours');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container d-grid gap-2\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"col pt-2\",\n        children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: ['fat', 'palette']\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this), \"\\xA0 Colours List\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this), canCreate && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-auto pt-3\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          tag: Link,\n          to: routes.colours.routes.new(),\n          outline: true,\n          color: \"success\",\n          children: [/*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n            icon: ['fat', 'plus']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 15\n          }, this), \"\\xA0 New Colour\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n      className: \"table\",\n      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n          className: \"sticky-top bg-white\",\n          style: {\n            top: '140px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"\\xA0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n            children: \"Hex\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n        children: colours.map(colour => /*#__PURE__*/_jsxDEV(\"tr\", {\n          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: routes.colours.routes.detail.to(colour._id),\n              children: /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n                icon: ['fat', 'edit']\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: colour.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n            children: colour.hex\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 15\n          }, this)]\n        }, colour._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n}\n_s(List, \"UCPabFWULQ9sotCsWvaEEHh2kzc=\", true);\n_c = List;\nexport default List;\nvar _c;\n$RefreshReg$(_c, \"List\");", "map": {"version": 3, "names": ["List", "colours", "useSelector", "selectColours", "isInRole", "useAuth", "canCreate", "Link", "routes", "new", "top", "map", "colour", "detail", "to", "_id", "name", "hex"], "sources": ["C:/Users/<USER>/Documents/projects/GreenRP/Web/ClientApp/src/features/colours/List.tsx"], "sourcesContent": ["\r\nexport function List() { \r\n  const colours = useSelector(selectColours),\r\n    { isInRole } = useAuth(),\r\n    canCreate = isInRole('create:colours');\r\n\r\n  return (\r\n    <div className=\"container d-grid gap-2\">\r\n      <div className=\"row sticky-top-navbar bg-white mt-2 py-2 border-bottom shadow\">\r\n        <h1 className=\"col pt-2\">\r\n          <FontAwesomeIcon icon={['fat', 'palette']} />\r\n          &nbsp;\r\n          Colours List\r\n        </h1>\r\n        {canCreate &&\r\n          <div className=\"col-auto pt-3\">\r\n            <Button tag={Link} to={routes.colours.routes.new()} outline color=\"success\">\r\n              <FontAwesomeIcon icon={['fat', 'plus']} />\r\n              &nbsp;\r\n              New Colour\r\n            </Button>\r\n          </div>\r\n        }\r\n      </div>\r\n      <table className=\"table\">\r\n        <thead>\r\n          <tr className=\"sticky-top bg-white\" style={{top: '140px'}}>\r\n            <th>&nbsp;</th>\r\n            <th>Name</th>\r\n            <th>Hex</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          {colours.map(colour =>\r\n            <tr key={colour._id}>\r\n              <td>\r\n                <Link to={routes.colours.routes.detail.to(colour._id)}>\r\n                  <FontAwesomeIcon icon={['fat', 'edit']} />\r\n                </Link>\r\n              </td>\r\n              <td>{colour.name}</td>\r\n              <td>{colour.hex}</td>\r\n            </tr>\r\n            )}\r\n        </tbody>\r\n      </table>      \r\n    </div>\r\n  )\r\n}\r\n\r\nexport default List;"], "mappings": ";;;AACA,OAAO,SAASA,IAAI,GAAG;EAAA;EACrB,MAAMC,OAAO,GAAGC,WAAW,CAACC,aAAa,CAAC;IACxC;MAAEC;IAAS,CAAC,GAAGC,OAAO,EAAE;IACxBC,SAAS,GAAGF,QAAQ,CAAC,gBAAgB,CAAC;EAExC,oBACE;IAAK,SAAS,EAAC,wBAAwB;IAAA,wBACrC;MAAK,SAAS,EAAC,+DAA+D;MAAA,wBAC5E;QAAI,SAAS,EAAC,UAAU;QAAA,wBACtB,QAAC,eAAe;UAAC,IAAI,EAAE,CAAC,KAAK,EAAE,SAAS;QAAE;UAAA;UAAA;UAAA;QAAA,QAAG;MAAA;QAAA;QAAA;QAAA;MAAA,QAG1C,EACJE,SAAS,iBACR;QAAK,SAAS,EAAC,eAAe;QAAA,uBAC5B,QAAC,MAAM;UAAC,GAAG,EAAEC,IAAK;UAAC,EAAE,EAAEC,MAAM,CAACP,OAAO,CAACO,MAAM,CAACC,GAAG,EAAG;UAAC,OAAO;UAAC,KAAK,EAAC,SAAS;UAAA,wBACzE,QAAC,eAAe;YAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;UAAE;YAAA;YAAA;YAAA;UAAA,QAAG;QAAA;UAAA;UAAA;UAAA;QAAA;MAGnC;QAAA;QAAA;QAAA;MAAA,QACL;IAAA;MAAA;MAAA;MAAA;IAAA,QAEJ,eACN;MAAO,SAAS,EAAC,OAAO;MAAA,wBACtB;QAAA,uBACE;UAAI,SAAS,EAAC,qBAAqB;UAAC,KAAK,EAAE;YAACC,GAAG,EAAE;UAAO,CAAE;UAAA,wBACxD;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAe,eACf;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAa,eACb;YAAA;UAAA;YAAA;YAAA;YAAA;UAAA,QAAY;QAAA;UAAA;UAAA;UAAA;QAAA;MACT;QAAA;QAAA;QAAA;MAAA,QACC,eACR;QAAA,UACGT,OAAO,CAACU,GAAG,CAACC,MAAM,iBACjB;UAAA,wBACE;YAAA,uBACE,QAAC,IAAI;cAAC,EAAE,EAAEJ,MAAM,CAACP,OAAO,CAACO,MAAM,CAACK,MAAM,CAACC,EAAE,CAACF,MAAM,CAACG,GAAG,CAAE;cAAA,uBACpD,QAAC,eAAe;gBAAC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM;cAAE;gBAAA;gBAAA;gBAAA;cAAA;YAAG;cAAA;cAAA;cAAA;YAAA;UACrC;YAAA;YAAA;YAAA;UAAA,QACJ,eACL;YAAA,UAAKH,MAAM,CAACI;UAAI;YAAA;YAAA;YAAA;UAAA,QAAM,eACtB;YAAA,UAAKJ,MAAM,CAACK;UAAG;YAAA;YAAA;YAAA;UAAA,QAAM;QAAA,GAPdL,MAAM,CAACG,GAAG;UAAA;UAAA;UAAA;QAAA,QAQd;MACJ;QAAA;QAAA;QAAA;MAAA,QACG;IAAA;MAAA;MAAA;MAAA;IAAA,QACF;EAAA;IAAA;IAAA;IAAA;EAAA,QACJ;AAEV;AAAC,GA/Cef,IAAI;AAAA,KAAJA,IAAI;AAiDpB,eAAeA,IAAI;AAAC;AAAA"}, "metadata": {}, "sourceType": "module"}