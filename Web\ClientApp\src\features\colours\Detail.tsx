
import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useParams, useNavigate } from 'react-router';
import { Link } from 'react-router-dom';
import { Button, Input, InputGroup, InputGroupText } from 'reactstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { routes } from 'app/routes';
import { useAuth } from 'features/auth/use-auth';
import { selectColours } from './colours-slice';
import { deleteColour, saveColour, selectColour, setColour } from './detail-slice';
import { createColour } from 'api/models/colour';
import { handleFocus } from 'utils/focus';

export function Detail() {
  const dispatch = useDispatch(),
    navigate = useNavigate(),
    { isInRole } = useAuth(),
    { id } = useParams<{ id: string }>(),
    colours = useSelector(selectColours),
    colour = useSelector(selectColour),
    isNew = !colour._rev,
    canUpdate = (isNew && isInRole('create:colours')) || isInRole('update:colours'),
    canDelete = isInRole('delete:colours');

  useEffect(() => {
    const found = colours.find(c => c._id === id);
    if (found && found._id !== colour._id) {
      dispatch(setColour(found));
    } else if (id === 'new' && colour._rev) {
      dispatch(setColour(createColour('', '#ffffff')));
    }
  }, [dispatch, id, colour, colours]);

  useEffect(() => {
    return function cleanup() {
      dispatch(setColour(createColour('', '#ffffff')));
    };
  }, [dispatch]);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value,
      update = { ...colour, name };

    dispatch(setColour(update));
  };

  const handleHexChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const hex = e.target.value,
      update = { ...colour, hex };

    dispatch(setColour(update));
  };

  const handleSaveClick = async () => {
    const result: any = await dispatch(saveColour());

    if (!result.error) {
      navigate(routes.colours.path);
    }
  };

  const handleDeleteClick = async () => {
    const result: any = await dispatch(deleteColour());

    if (!result.error) {
      navigate(routes.colours.path);
    }
  };

  return (
    <div className="container d-grid gap-3">
      <div className="row sticky-top-navbar my-2 py-2 bg-white shadow">
        <div className="col-auto pt-3">
          <Link to={routes.colours.path}>
            <FontAwesomeIcon icon={['fat', 'chevron-left']} />
            &nbsp; Back to Colours List
          </Link>
        </div>
        <h1 className="col">{isNew ? 'New Colour' : colour.name}</h1>
      </div>
      <div className="row">
        <div className="col-12 col-md-4">
          <label htmlFor="colour-name">Name</label>
          <Input
            id="colour-name"
            value={colour.name}
            onChange={handleNameChange}
            disabled={!canUpdate}
          />
        </div>
        <div className="col-12 col-md-4">
          <label htmlFor="colour-hex">Hex Value</label>
          <InputGroup>
            <Input
              id="colour-hex"
              type="color"
              value={colour.hex}
              onChange={handleHexChange}
              disabled={!canUpdate}
            />
            <Input
              type="text"
              value={colour.hex}
              onChange={handleHexChange}
              onFocus={handleFocus}
              disabled={!canUpdate}
              placeholder="#ffffff"
            />
          </InputGroup>
        </div>
        <div className="col-12 col-md-4">
          <label>Preview</label>
          <InputGroup>
            <InputGroupText
              style={{
                backgroundColor: colour.hex,
                color: colour.hex,
                minWidth: '50px'
              }}
            >
              &nbsp;
            </InputGroupText>
            <InputGroupText>{colour.hex}</InputGroupText>
          </InputGroup>
        </div>
      </div>
      <div className="row sticky-bottom bg-white border-top py-2">
        {!isNew && canDelete && (
          <div className="col-auto">
            <Button
              onClick={handleDeleteClick}
              outline
              color="danger"
              size="lg"
              className="me-auto">
              <FontAwesomeIcon icon={['fat', 'trash-alt']} />
              &nbsp; Delete
            </Button>
          </div>
        )}
        <div className="col text-end">
          <Button tag={Link} to={routes.colours.path} outline size="lg">
            {canUpdate ? 'Cancel' : 'Close'}
          </Button>
          {canUpdate && (
            <>
              &nbsp;
              <Button onClick={handleSaveClick} color="success" size="lg">
                <FontAwesomeIcon icon={['fat', 'save']} />
                &nbsp; Save
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export default Detail;